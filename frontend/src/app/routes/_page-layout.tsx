import React from 'react'
import { createFileRoute, Outlet } from '@tanstack/react-router'
import { HeaderSlotContext } from '@/services/header'
import { Sidebar } from '@/features/sidebar'

export const Route = createFileRoute('/_page-layout')({
    component: AppLayout,
})

function AppLayout() {
    const [headerSlot, setHeaderSlot] = React.useState<HTMLElement | null>(null)

    const headerSlotRef = React.useCallback((el: HTMLElement | null) => {
        setHeaderSlot(el)
    }, [])

    return (
        <div className="flex flex-row h-screen">
            <div className="flex-col">
                <Sidebar />
            </div>
            <main className="flex flex-col w-full p-6 gap-6">
                <div
                    ref={headerSlotRef}
                    id="header-slot"
                />
                <HeaderSlotContext value={headerSlot}>
                    <div className="rounded-lg border bg-card p-6 flex-1">
                        <Outlet />
                    </div>
                </HeaderSlotContext>
            </main>
        </div>
    )
}
