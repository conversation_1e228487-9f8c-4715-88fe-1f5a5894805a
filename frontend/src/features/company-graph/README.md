# Company Graph Feature

A modern, performant React component for visualizing organizational structures using React Flow and TanStack Query.

## 🚀 Features

- **Modern React Patterns**: Uses hooks, memo, and proper TypeScript types
- **Data Fetching**: TanStack Query for server state management with caching and error handling
- **Performance Optimized**: Memoized components and calculations to prevent unnecessary re-renders
- **Error Handling**: Comprehensive error boundaries and loading states
- **Accessibility**: ARIA labels and keyboard navigation support
- **Responsive Design**: Works on different screen sizes
- **Layout Engine**: Automatic graph layout using Dagre algorithm

## 📁 Structure

```
company-graph/
├── components/           # React components
│   ├── company-graph-flow.tsx          # Main graph rendering component
│   ├── company-graph-loading.tsx       # Loading state component
│   ├── company-graph-error.tsx         # Error state component
│   ├── company-graph-error-boundary.tsx # Error boundary wrapper
│   └── index.ts                        # Component exports
├── hooks/               # Custom React hooks
│   ├── use-organizational-data.ts      # Data fetching with TanStack Query
│   ├── use-graph-layout.ts            # Graph layout calculations
│   ├── use-viewport-management.ts     # Viewport control and focus
│   └── index.ts                       # Hook exports
├── ui/                  # UI components
│   └── employee-node.tsx              # Individual employee node component
├── data/                # Data and mock data
│   └── mock-employees.ts              # Sample organizational data
├── types.ts             # TypeScript type definitions
├── company-graph.tsx    # Main public component
├── index.ts             # Feature exports
└── README.md           # This file
```

## 🔧 Usage

### Basic Usage

```tsx
import { CompanyGraph } from '@/features/company-graph'

function OrganizationPage() {
    return (
        <div className="w-full h-screen">
            <CompanyGraph />
        </div>
    )
}
```

### Advanced Usage

```tsx
import { CompanyGraph } from '@/features/company-graph'

function OrganizationPage() {
    return (
        <div className="w-full h-screen">
            <CompanyGraph
                layoutDirection="LR"  // Left to right layout
                autoFocus={true}      // Auto-focus on CEO
                focusNodeId="ceo-001" // Custom focus node
                className="custom-graph"
            />
        </div>
    )
}
```

## 🎛️ Props

### CompanyGraph

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `layoutDirection` | `'TB' \| 'LR' \| 'BT' \| 'RL'` | `'TB'` | Graph layout direction |
| `autoFocus` | `boolean` | `true` | Auto-focus on initial node |
| `focusNodeId` | `string` | `'ceo-001'` | Node ID to focus on |
| `className` | `string` | `''` | Additional CSS classes |

## 🔗 Hooks

### useOrganizationalData

Fetches organizational data using TanStack Query with caching and error handling.

```tsx
const { data, isLoading, error, refetch } = useOrganizationalData()
```

### useGraphLayout

Calculates graph layout using Dagre algorithm with memoization.

```tsx
const { nodes, edges, changeDirection } = useGraphLayout(
    initialNodes,
    initialEdges,
    { direction: 'TB' }
)
```

### useViewportManagement

Manages React Flow viewport with responsive positioning and proper error handling.

```tsx
const {
    focusOnNode,
    centerView,
    resetViewport,
    containerDimensions
} = useViewportManagement({
    focusNodeId: 'ceo-001',
    autoFocus: true,
    rootNodePositionRatio: 0.25, // Position at 25% from top
    minVerticalOffset: 50,        // Minimum offset for small screens
    maxVerticalOffset: 400        // Maximum offset for large screens
})
```

#### Responsive Positioning

The viewport management automatically calculates optimal positioning based on:

- **Container dimensions**: Dynamically measures the React Flow container
- **Screen size**: Adapts to mobile, tablet, and desktop viewports
- **Position ratio**: Configurable positioning (0.0 = top, 0.5 = center, 1.0 = bottom)
- **Constraints**: Min/max offsets prevent extreme positioning

#### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `rootNodePositionRatio` | `number` | `0.25` | Position ratio from top (0.0-1.0) |
| `minVerticalOffset` | `number` | `50` | Minimum offset in pixels |
| `maxVerticalOffset` | `number` | `400` | Maximum offset in pixels |
| `autoFocus` | `boolean` | `true` | Auto-focus on initialization |
| `focusDelay` | `number` | `100` | Delay before focusing (ms) |

## 🎨 Styling

The component uses Tailwind CSS with level-based styling:

- **Level 0 (CEO)**: Purple gradient
- **Level 1 (C-Level)**: Blue gradient
- **Level 2 (Department Heads)**: Green gradient
- **Level 3 (Senior Staff)**: Orange gradient
- **Others**: Gray gradient

## 📱 Responsive Design

### Viewport Management

The component automatically adapts to different screen sizes:

#### Mobile (< 768px)
- Root node positioned at 20% from top
- Minimum offset: 30px
- Optimized touch interactions

#### Tablet (768px - 1024px)
- Root node positioned at 25% from top
- Balanced view for portrait/landscape

#### Desktop (> 1024px)
- Root node positioned at 25% from top
- Maximum offset: 400px for large screens

### Dynamic Calculations

```tsx
// Example: Custom responsive positioning
useViewportManagement({
    rootNodePositionRatio: window.innerWidth < 768 ? 0.2 : 0.25,
    minVerticalOffset: window.innerWidth < 768 ? 30 : 50,
    maxVerticalOffset: window.innerWidth < 768 ? 200 : 400,
})
```

### Container Awareness

The viewport management automatically:
- Measures the React Flow container dimensions
- Updates positioning on window resize
- Maintains optimal viewing regardless of container size
- Handles dynamic layout changes

## 🔄 Data Flow

1. **Data Fetching**: `useOrganizationalData` hook fetches data via TanStack Query
2. **Layout Calculation**: `useGraphLayout` applies Dagre layout algorithm
3. **Rendering**: `CompanyGraphFlow` renders the graph with React Flow
4. **Viewport Management**: `useViewportManagement` handles focus and navigation

## 🚨 Error Handling

- **Error Boundary**: Catches JavaScript errors in the component tree
- **Query Errors**: TanStack Query handles network and data errors
- **Loading States**: Skeleton loaders during data fetching
- **Retry Logic**: Automatic retry with exponential backoff

## ⚡ Performance

- **React.memo**: Prevents unnecessary component re-renders
- **useMemo**: Memoizes expensive calculations
- **useCallback**: Memoizes event handlers
- **Layout Caching**: Dagre calculations are memoized
- **Image Optimization**: Lazy loading and fallback avatars

## 🧪 Testing

Components include `data-testid` attributes for testing:

```tsx
// Test selectors
'[data-testid="company-graph-flow"]'
'[data-testid="company-graph-loading"]'
'[data-testid="company-graph-error"]'
'[data-testid="employee-node-ceo-001"]'
```

## 🔧 Development

### Adding New Employee Levels

1. Update `LEVEL_STYLES` in `employee-node.tsx`
2. Add level to `Employee` type in `types.ts`
3. Update mock data in `mock-employees.ts`

### Customizing Layout

Modify layout options in `useGraphLayout`:

```tsx
const { nodes, edges } = useGraphLayout(nodes, edges, {
    direction: 'TB',
    nodeWidth: 250,      // Custom node width
    nodeHeight: 150,     // Custom node height
    nodeSpacing: 30,     // Space between nodes
    rankSpacing: 40      // Space between levels
})
```

## 🐛 Common Issues

### useEffect with Promises Error
❌ **Before**: `useEffect(() => { fitView().then(...) })`
✅ **After**: Uses `useViewportManagement` hook with proper async handling

### Performance Issues
❌ **Before**: Layout calculated on every render
✅ **After**: Memoized with `useMemo` and dependency arrays

### Memory Leaks
❌ **Before**: Global Dagre instance shared across renders
✅ **After**: New instance created per calculation with cleanup
