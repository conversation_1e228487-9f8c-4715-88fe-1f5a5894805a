import { memo, useMemo } from 'react'
import {
    ReactFlow,
    Background,
    BackgroundVariant,
    useNodesState,
    useEdgesState,
    type Node,
    type Edge,
} from '@xyflow/react'

import { EmployeeNode } from './ui/employee-node'
import { useGraphLayout } from './hooks'
import type { EmployeeNodeData } from './types'
import { useViewportManagement } from './hooks/use-viewport-management'

// Memoized node types to prevent React Flow re-renders
const nodeTypes = {
    employee: EmployeeNode,
} as const

interface CompanyGraphFlowProps {
    initialNodes: Node<EmployeeNodeData>[]
    initialEdges: Edge[]
    focusNodeId: string
    autoFocus?: boolean
    className?: string
}

export const CompanyGraphFlow = memo(function CompanyGraphFlow({
    initialNodes,
    initialEdges,
    focusNodeId,
    autoFocus = true,
    className = '',
}: CompanyGraphFlowProps) {
    const { nodes: layoutedNodes, edges: layoutedEdges } = useGraphLayout(
        initialNodes,
        initialEdges,
        { nodeHeight: 150 },
    )

    const [
        nodes,
        ,
        onNodesChange,
    ] = useNodesState(layoutedNodes)
    const [
        edges,
        ,
        onEdgesChange,
    ] = useEdgesState(layoutedEdges)

    // Handle viewport management with responsive positioning
    useViewportManagement({
        focusNodeId,
        autoFocus,
        padding: 5,
        rootNodePositionRatio: 0.25, // Position CEO at 25% from top
    })

    const reactFlowProps = useMemo(
        () => ({
            nodes,
            edges,
            onNodesChange,
            onEdgesChange,
            nodeTypes,
            nodesConnectable: false,
            proOptions: { hideAttribution: true },
            minZoom: 0.2,
            maxZoom: 5,
        }),
        [
            nodes,
            edges,
            onNodesChange,
            onEdgesChange,
        ],
    )

    const backgroundProps = useMemo(
        () => ({
            variant: BackgroundVariant.Dots,
            gap: 40,
            size: 0.8,
            color: '#e2e8f0',
            style: { backgroundColor: 'transparent', opacity: 0.3 },
        }),
        [],
    )

    return (
        <div
            className={`w-full h-full bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100 relative ${className}`}
            data-testid="company-graph-flow"
        >
            <ReactFlow {...reactFlowProps}>
                <Background {...backgroundProps} />
            </ReactFlow>
        </div>
    )
})
