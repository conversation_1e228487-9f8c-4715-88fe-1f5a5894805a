import { useEffect } from 'react'
import dagre from '@dagrejs/dagre'
import {
    ReactFlow,
    ReactFlowProvider,
    Background,
    BackgroundVariant,
    useNodesState,
    useEdgesState,
    useReactFlow,
    Position,
    type Node,
    type Edge,
} from '@xyflow/react'

import { EmployeeNode } from './ui/employee-node'
import { initialEdges, initialNodes } from './data/mock-employees'
import type { EmployeeNodeData } from './types'

// Memoize nodeTypes to prevent React Flow re-renders
const nodeTypes = {
    employee: EmployeeNode,
} as const

// Dagre graph instance
const dagreGraph = new dagre.graphlib.Graph()
dagreGraph.setDefaultEdgeLabel(() => ({}))

// Node dimensions for layout calculation
const nodeWidth = 200
const nodeHeight = 120

const getLayoutedElements = (
    nodes: Node<EmployeeNodeData>[],
    edges: Edge[],
    direction: 'TB' | 'LR' = 'TB',
) => {
    const isHorizontal = direction === 'LR'
    dagreGraph.setGraph({ rankdir: direction, nodesep: 25, ranksep: 25 })

    for (const node of nodes) {
        dagreGraph.setNode(node.id, { width: nodeWidth, height: nodeHeight })
    }

    for (const edge of edges) {
        dagreGraph.setEdge(edge.source, edge.target)
    }

    dagre.layout(dagreGraph, {})

    const newNodes = nodes.map((node) => {
        const nodeWithPosition = dagreGraph.node(node.id)
        const newNode = {
            ...node,
            targetPosition: isHorizontal ? Position.Left : Position.Top,
            sourcePosition: isHorizontal ? Position.Right : Position.Bottom,
            position: {
                x: nodeWithPosition.x - nodeWidth / 2,
                y: nodeWithPosition.y - nodeHeight / 2,
            },
        }

        return newNode
    })

    return { nodes: newNodes, edges }
}

// Get initial layouted elements
const { nodes: layoutedNodes, edges: layoutedEdges } = getLayoutedElements(
    initialNodes,
    initialEdges,
    'TB',
)

function CompanyGraphFlow() {
    const { setViewport, fitView, getViewport, viewportInitialized } = useReactFlow()
    const [
        nodes,
        ,
        onNodesChange,
    ] = useNodesState(layoutedNodes)
    const [
        edges,
        ,
        onEdgesChange,
    ] = useEdgesState(layoutedEdges)

    useEffect(() => {
        if (viewportInitialized) {
            fitView({ nodes: [{ id: 'ceo-001' }], padding: 5 }).then(() => {
                const vp = getViewport()
                setViewport({ x: vp.x, y: vp.y - 300, zoom: vp.zoom })
            })
        }
    }, [viewportInitialized])

    return (
        <div className="w-full h-full bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100 relative">
            <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                nodeTypes={nodeTypes}
                nodesConnectable={false}
                // fitView
                proOptions={{ hideAttribution: true }}
                minZoom={0.2}
                maxZoom={5}
            >
                <Background
                    variant={BackgroundVariant.Dots}
                    gap={40}
                    size={0.8}
                    color="#e2e8f0"
                    style={{ backgroundColor: 'transparent', opacity: 0.3 }}
                />
            </ReactFlow>
        </div>
    )
}

// Main component with ReactFlowProvider wrapper
export function CompanyGraph() {
    return (
        <ReactFlowProvider>
            <CompanyGraphFlow />
        </ReactFlowProvider>
    )
}
