import { memo } from 'react'
import { ReactFlowProvider } from '@xyflow/react'

import { useOrganizationalData } from './hooks'
import {
    CompanyGraphFlow,
    CompanyGraphLoading,
    CompanyGraphError,
    CompanyGraphErrorBoundary,
} from './components'

interface CompanyGraphProps {
    /**
     * Layout direction for the organizational chart
     * @default 'TB' (top to bottom)
     */
    /**
     * Whether to automatically focus on the CEO node when loaded
     * @default true
     */
    autoFocus?: boolean
    /**
     * Node ID to focus on initially
     * @default 'ceo-001'
     */
    focusNodeId?: string
    /**
     * Additional CSS classes for the container
     */
    className?: string
}

/**
 * Main Company Graph component with ReactFlowProvider wrapper and error boundary
 * This is the public API component that should be used by consumers
 */
export const CompanyGraph = memo(function CompanyGraph({
    autoFocus,
    className,
}: CompanyGraphProps = {}) {
    const { data, isLoading, error, refetch, isRefetching } = useOrganizationalData()

    if (isLoading) {
        return <CompanyGraphLoading />
    }

    if (error || !data) {
        return (
            <CompanyGraphError
                error={error || 'Не удалось загрузить данные'}
                onRetry={refetch}
                isRetrying={isRefetching}
            />
        )
    }

    if (!data) {
        return null
    }
    return (
        <CompanyGraphErrorBoundary showDetails={process.env.NODE_ENV === 'development'}>
            <ReactFlowProvider>
                <CompanyGraphFlow
                    initialNodes={data.nodes}
                    initialEdges={data.edges}
                    autoFocus={autoFocus}
                    focusNodeId={'ceo-001'}
                    className={className}
                />
            </ReactFlowProvider>
        </CompanyGraphErrorBoundary>
    )
})

CompanyGraph.displayName = 'CompanyGraph'
