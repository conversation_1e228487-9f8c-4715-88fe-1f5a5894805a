import { Component, type ReactNode, type ErrorInfo } from 'react'
import { <PERSON>ert<PERSON><PERSON>gle, RefreshCw } from 'lucide-react'

interface ErrorBoundaryState {
    hasError: boolean
    error?: Error
    errorInfo?: ErrorInfo
}

interface CompanyGraphErrorBoundaryProps {
    children: ReactNode
    /**
     * Callback when an error occurs
     */
    onError?: (error: Error, errorInfo: ErrorInfo) => void
    /**
     * Custom fallback component
     */
    fallback?: (error: Error, retry: () => void) => ReactNode
    /**
     * Whether to show detailed error information
     */
    showDetails?: boolean
}

/**
 * Error boundary specifically designed for the company graph
 * Catches JavaScript errors anywhere in the child component tree
 */
export class CompanyGraphErrorBoundary extends Component<
    CompanyGraphErrorBoundaryProps,
    ErrorBoundaryState
> {
    constructor(props: CompanyGraphErrorBoundaryProps) {
        super(props)
        this.state = { hasError: false }
    }

    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
        // Update state so the next render will show the fallback UI
        return {
            hasError: true,
            error,
        }
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        // Log the error to console and external service
        console.error('CompanyGraph Error Boundary caught an error:', error, errorInfo)

        // Call the onError callback if provided
        this.props.onError?.(error, errorInfo)

        // Store error info in state
        this.setState({
            errorInfo,
        })
    }

    handleRetry = () => {
        // Reset the error boundary state
        this.setState({
            hasError: false,
            error: undefined,
            errorInfo: undefined,
        })
    }

    render() {
        if (this.state.hasError) {
            const { error } = this.state
            const { fallback, showDetails = false } = this.props

            // Use custom fallback if provided
            if (fallback && error) {
                return fallback(error, this.handleRetry)
            }

            // Default error UI
            return (
                <div
                    className="w-full h-full bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100 relative flex items-center justify-center"
                    data-testid="company-graph-error-boundary"
                >
                    <div className="max-w-lg mx-auto text-center p-8">
                        {/* Error icon */}
                        <div className="flex justify-center mb-6">
                            <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center">
                                <AlertTriangle className="w-10 h-10 text-red-600" />
                            </div>
                        </div>

                        {/* Error content */}
                        <div className="space-y-4">
                            <h2 className="text-2xl font-bold text-gray-900">
                                Что-то пошло не так
                            </h2>

                            <p className="text-gray-600 leading-relaxed">
                                Произошла неожиданная ошибка при отображении структуры компании. Мы
                                уже работаем над исправлением проблемы.
                            </p>

                            {/* Technical error details */}
                            {showDetails && error && (
                                <details className="mt-6 text-left">
                                    <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700 mb-2">
                                        Техническая информация
                                    </summary>
                                    <div className="space-y-2">
                                        <div className="p-3 bg-red-50 rounded-lg border border-red-200">
                                            <h4 className="text-sm font-semibold text-red-800 mb-1">
                                                Ошибка:
                                            </h4>
                                            <p className="text-xs text-red-700 font-mono break-all">
                                                {error.message}
                                            </p>
                                        </div>

                                        {error.stack && (
                                            <div className="p-3 bg-gray-50 rounded-lg border text-xs text-gray-700 font-mono max-h-32 overflow-y-auto">
                                                <h4 className="text-sm font-semibold text-gray-800 mb-1">
                                                    Stack trace:
                                                </h4>
                                                <pre className="whitespace-pre-wrap break-all">
                                                    {error.stack}
                                                </pre>
                                            </div>
                                        )}
                                    </div>
                                </details>
                            )}

                            {/* Action buttons */}
                            <div className="flex flex-col sm:flex-row gap-3 pt-6">
                                <button
                                    onClick={this.handleRetry}
                                    className="inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                                    data-testid="error-boundary-retry"
                                >
                                    <RefreshCw className="w-4 h-4 mr-2" />
                                    Попробовать снова
                                </button>

                                <button
                                    onClick={() => globalThis.location.reload()}
                                    className="inline-flex items-center justify-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200"
                                >
                                    Перезагрузить страницу
                                </button>
                            </div>
                        </div>

                        {/* Help text */}
                        <div className="mt-8 pt-6 border-t border-gray-200">
                            <p className="text-sm text-gray-500">
                                Если проблема повторяется, обратитесь в службу поддержки.
                            </p>
                        </div>
                    </div>
                </div>
            )
        }

        return this.props.children
    }
}

/**
 * HOC for wrapping components with error boundary
 */
export function withErrorBoundary<P extends object>(
    Component: React.ComponentType<P>,
    errorBoundaryProps?: Omit<CompanyGraphErrorBoundaryProps, 'children'>,
) {
    const WrappedComponent = (props: P) => (
        <CompanyGraphErrorBoundary {...errorBoundaryProps}>
            <Component {...props} />
        </CompanyGraphErrorBoundary>
    )

    WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`

    return WrappedComponent
}
