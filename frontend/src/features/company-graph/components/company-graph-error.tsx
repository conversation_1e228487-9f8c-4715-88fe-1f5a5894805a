import { memo } from 'react'
import { AlertCircle, RefreshCw } from 'lucide-react'

interface CompanyGraphErrorProps {
    /**
     * Error message to display
     */
    error?: Error | string
    /**
     * Callback to retry loading the data
     */
    onRetry?: () => void
    /**
     * Whether the retry action is currently loading
     */
    isRetrying?: boolean
}

/**
 * Error component for the company graph
 * Displays error information and provides retry functionality
 */
export const CompanyGraphError = memo(function CompanyGraphError({
    error,
    onRetry,
    isRetrying = false,
}: CompanyGraphErrorProps) {
    const errorMessage = error instanceof Error ? error.message : error || 'Произошла неизвестная ошибка'

    return (
        <div 
            className="w-full h-full bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100 relative flex items-center justify-center"
            data-testid="company-graph-error"
        >
            <div className="max-w-md mx-auto text-center p-8">
                {/* Error icon */}
                <div className="flex justify-center mb-6">
                    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                        <AlertCircle className="w-8 h-8 text-red-600" />
                    </div>
                </div>

                {/* Error content */}
                <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900">
                        Ошибка загрузки данных
                    </h3>
                    
                    <p className="text-gray-600 leading-relaxed">
                        Не удалось загрузить структуру компании. Проверьте подключение к интернету и попробуйте снова.
                    </p>

                    {/* Technical error details */}
                    {error && (
                        <details className="mt-4 text-left">
                            <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                                Техническая информация
                            </summary>
                            <div className="mt-2 p-3 bg-gray-50 rounded-lg border text-xs text-gray-700 font-mono break-all">
                                {errorMessage}
                            </div>
                        </details>
                    )}

                    {/* Retry button */}
                    {onRetry && (
                        <div className="pt-4">
                            <button
                                onClick={onRetry}
                                disabled={isRetrying}
                                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                                data-testid="retry-button"
                            >
                                <RefreshCw 
                                    className={`w-4 h-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} 
                                />
                                {isRetrying ? 'Повторная загрузка...' : 'Попробовать снова'}
                            </button>
                        </div>
                    )}
                </div>

                {/* Help text */}
                <div className="mt-8 pt-6 border-t border-gray-200">
                    <p className="text-sm text-gray-500">
                        Если проблема повторяется, обратитесь к администратору системы.
                    </p>
                </div>
            </div>
        </div>
    )
})

CompanyGraphError.displayName = 'CompanyGraphError'
