import { memo } from 'react'

/**
 * Loading component for the company graph
 * Displays a skeleton loader while data is being fetched
 */
export const CompanyGraphLoading = memo(function CompanyGraphLoading() {
    return (
        <div
            className="w-full h-full bg-gradient-to-br z-50 from-slate-100 via-blue-50 to-indigo-100 relative flex items-center justify-center"
            data-testid="company-graph-loading"
        >
            <div className="flex flex-col items-center space-y-4">
                {/* Main loading spinner */}
                <div className="relative">
                    <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
                    <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-blue-400 rounded-full animate-spin animation-delay-150"></div>
                </div>

                {/* Loading text */}
                <div className="text-center">
                    <h3 className="text-lg font-semibold text-gray-700 mb-2">
                        Загрузка структуры компании
                    </h3>
                    <p className="text-sm text-gray-500">Подготавливаем организационную схему...</p>
                </div>

                {/* Skeleton nodes */}
                <div className="flex flex-col items-center space-y-8 mt-8">
                    {/* CEO level */}
                    <div className="w-48 h-24 bg-white/60 rounded-2xl border border-gray-200/50 animate-pulse shadow-sm"></div>

                    {/* C-level */}
                    <div className="flex space-x-6">
                        {Array.from({ length: 4 }).map((_, i) => (
                            <div
                                key={i}
                                className="w-40 h-20 bg-white/50 rounded-xl border border-gray-200/50 animate-pulse shadow-sm"
                                style={{ animationDelay: `${i * 100}ms` }}
                            ></div>
                        ))}
                    </div>

                    {/* Department level */}
                    <div className="flex space-x-4">
                        {Array.from({ length: 6 }).map((_, i) => (
                            <div
                                key={i}
                                className="w-32 h-16 bg-white/40 rounded-lg border border-gray-200/50 animate-pulse shadow-sm"
                                style={{ animationDelay: `${i * 80}ms` }}
                            ></div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    )
})

CompanyGraphLoading.displayName = 'CompanyGraphLoading'
