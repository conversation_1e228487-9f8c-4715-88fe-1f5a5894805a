// import { stratify, tree } from 'd3-hierarchy' // TODO: Install d3-hierarchy
import type { Edge } from '@xyflow/react'
import type { Employee } from '../types'

export const mockEmployees: Employee[] = [
    // --- Уровень 0: CEO ---
    {
        id: 'ceo-001',
        name: '<PERSON><PERSON><PERSON>трова',
        position: 'Генеральный директор',
        department: 'Руководство',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-67',
        avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=face',
        level: 0,
    },

    // --- Уровень 1: C-Level ---
    {
        id: 'cto-001',
        name: 'Д<PERSON><PERSON><PERSON><PERSON><PERSON>ван<PERSON>',
        position: 'Технический директор',
        department: 'IT',
        email: 'd.<PERSON><PERSON><PERSON>@simbios.com',
        phone: '+7 (495) 123-45-68',
        avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',
        level: 1,
    },
    {
        id: 'cfo-001',
        name: 'Елена Смирнова',
        position: 'Финансовый директор',
        department: 'Финансы',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-69',
        avatar: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150&h=150&fit=crop&crop=face',
        level: 1,
    },
    {
        id: 'cmo-001',
        name: 'Михаил Козлов',
        position: 'Директор по маркетингу',
        department: 'Маркетинг',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-70',
        avatar: 'https://images.unsplash.com/photo-1557862921-37829c790f19?w=150&h=150&fit=crop&crop=face',
        level: 1,
    },
    {
        id: 'chro-001',
        name: 'Светлана Соловьева',
        position: 'Директор по персоналу',
        department: 'HR',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-75',
        avatar: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=150&h=150&fit=crop&crop=face',
        level: 1,
    },

    // --- Уровень 2: Руководители отделов ---
    {
        id: 'dev-head-001',
        name: 'Сергей Волков',
        position: 'Руководитель разработки',
        department: 'IT',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-71',
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
        level: 2,
    },
    {
        id: 'qa-head-001',
        name: 'Ольга Морозова',
        position: 'Руководитель QA',
        department: 'IT',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-72',
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
        level: 2,
    },
    {
        id: 'finance-head-001',
        name: 'Александр Новиков',
        position: 'Главный бухгалтер',
        department: 'Финансы',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-73',
        avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face',
        level: 2,
    },
    {
        id: 'marketing-head-001',
        name: 'Татьяна Лебедева',
        position: 'Менеджер по продукту',
        department: 'Маркетинг',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-74',
        avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face',
        level: 2,
    },
    {
        id: 'hr-head-001',
        name: 'Ирина Зайцева',
        position: 'Руководитель отдела кадров',
        department: 'HR',
        email: '<EMAIL>',
        phone: '+7 (495) 123-45-76',
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
        level: 2,
    },

    // --- Уровень 3: Специалисты ---
    {
        id: 'dev-001',
        name: 'Игорь Соколов',
        position: 'Senior Frontend Developer',
        department: 'IT',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
        level: 3,
    },
    {
        id: 'dev-002',
        name: 'Мария Федорова',
        position: 'Senior Backend Developer',
        department: 'IT',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face',
        level: 3,
    },
    {
        id: 'dev-003',
        name: 'Павел Романов',
        position: 'DevOps Engineer',
        department: 'IT',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1633332755192-727a05c4013d?w=150&h=150&fit=crop&crop=face',
        level: 3,
    },
    {
        id: 'qa-001',
        name: 'Андрей Кузнецов',
        position: 'QA Engineer',
        department: 'IT',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?w=150&h=150&fit=crop&crop=face',
        level: 3,
    },
    {
        id: 'finance-001',
        name: 'Наталья Орлова',
        position: 'Финансовый аналитик',
        department: 'Финансы',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        level: 3,
    },
    {
        id: 'marketing-001',
        name: 'Владимир Попов',
        position: 'Маркетинг-аналитик',
        department: 'Маркетинг',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        level: 3,
    },
    {
        id: 'hr-001',
        name: 'Екатерина Виноградова',
        position: 'HR-менеджер',
        department: 'HR',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1554151228-14d9def656e4?w=150&h=150&fit=crop&crop=face',
        level: 3,
    },
    {
        id: 'marketing-002',
        name: 'Артем Беляев',
        position: 'SMM-менеджер',
        department: 'Маркетинг',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1522075469751-3a6694fb2f61?w=150&h=150&fit=crop&crop=face',
        level: 3,
    },

    // --- Уровень 4: Junior / Intern ---
    {
        id: 'dev-004',
        name: 'Кирилл Егоров',
        position: 'Junior Backend Developer',
        department: 'IT',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1547425260-76bc4c40042c?w=150&h=150&fit=crop&crop=face',
        level: 4,
    },
    {
        id: 'qa-002',
        name: 'Алиса Богданова',
        position: 'Junior QA Engineer',
        department: 'IT',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?w=150&h=150&fit=crop&crop=face',
        level: 4,
    },
    {
        id: 'marketing-003',
        name: 'Денис Давыдов',
        position: 'Стажер-маркетолог',
        department: 'Маркетинг',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1628157588553-5eeea00af15c?w=150&h=150&fit=crop&crop=face',
        level: 4,
    },
]

export const initialNodes = mockEmployees.map((employee) => {
    return {
        id: employee.id,
        type: 'employee' as const,
        position: { x: 0, y: 0 },
        data: { employee },
    }
})

export const initialEdges: Edge[] = [
    // CEO -> C-Level
    { id: 'e-ceo-cto', source: 'ceo-001', target: 'cto-001', animated: true },
    { id: 'e-ceo-cfo', source: 'ceo-001', target: 'cfo-001', animated: true },
    { id: 'e-ceo-cmo', source: 'ceo-001', target: 'cmo-001', animated: true },
    { id: 'e-ceo-chro', source: 'ceo-001', target: 'chro-001', animated: true },

    // C-Level -> Department Heads
    { id: 'e-cto-devhead', source: 'cto-001', target: 'dev-head-001' },
    { id: 'e-cto-qahead', source: 'cto-001', target: 'qa-head-001' },
    { id: 'e-cfo-finhead', source: 'cfo-001', target: 'finance-head-001' },
    { id: 'e-cmo-markhead', source: 'cmo-001', target: 'marketing-head-001' },
    { id: 'e-chro-hrhead', source: 'chro-001', target: 'hr-head-001' },

    // Department Heads -> Specialists
    { id: 'e-devhead-dev1', source: 'dev-head-001', target: 'dev-001' },
    { id: 'e-devhead-dev2', source: 'dev-head-001', target: 'dev-002' },
    { id: 'e-devhead-dev3', source: 'dev-head-001', target: 'dev-003' },
    { id: 'e-qahead-qa1', source: 'qa-head-001', target: 'qa-001' },
    { id: 'e-finhead-fin1', source: 'finance-head-001', target: 'finance-001' },
    { id: 'e-markhead-mark1', source: 'marketing-head-001', target: 'marketing-001' },
    { id: 'e-markhead-mark2', source: 'marketing-head-001', target: 'marketing-002' },
    { id: 'e-hrhead-hr1', source: 'hr-head-001', target: 'hr-001' },

    // Specialists -> Juniors/Interns
    { id: 'e-dev2-dev4', source: 'dev-002', target: 'dev-004' }, // Senior Backend -> Junior Backend
    { id: 'e-qa1-qa2', source: 'qa-001', target: 'qa-002' }, // QA -> Junior QA
    { id: 'e-mark1-mark3', source: 'marketing-001', target: 'marketing-003' }, // Analyst -> Intern
]

// Cache for organizational data to prevent recalculation
// let cachedOrganizationalData: OrganizationalData | null = null

// // Layout is now handled in the component

// // Transform employees into React Flow nodes and edges - With D3 Layout
// export function createOrganizationalData(): OrganizationalData {
//     // Return cached data if available
//     if (cachedOrganizationalData) {
//         return cachedOrganizationalData
//     }

//     // Create initial nodes without positioning
//     const nodes: EmployeeNode[] = mockEmployees.map((employee) => ({
//         id: employee.id,
//         type: 'employee' as const,
//         position: { x: 0, y: 0 }, // Will be updated by D3 layout
//         data: { employee },
//         // Performance optimizations
//         draggable: true,
//         selectable: true,
//     }))

//     const edges: EmployeeEdge[] = mockEmployees
//         .filter((employee) => employee.managerId)
//         .map((employee) => ({
//             id: `${employee.managerId}-${employee.id}`,
//             source: employee.managerId!,
//             target: employee.id,
//             type: 'smoothstep' as const,
//             data: { relationship: 'manages' as const },
//             style: {
//                 stroke: '#94a3b8',
//                 strokeWidth: 1.2, // Reduced for better performance
//                 strokeOpacity: 0.5,
//             },
//             markerEnd: {
//                 type: 'arrowclosed' as const,
//                 color: '#94a3b8',
//                 width: 10,
//                 height: 10,
//             },
//             // Performance optimizations
//             animated: false,
//             selectable: false,
//         }))

//     // Cache the result (layout will be applied in component)
//     cachedOrganizationalData = {
//         employees: mockEmployees,
//         nodes,
//         edges,
//     }

//     return cachedOrganizationalData
// }
