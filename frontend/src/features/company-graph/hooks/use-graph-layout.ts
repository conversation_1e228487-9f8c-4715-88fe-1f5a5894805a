import { useMemo } from 'react'
import dagre from '@dagrejs/dagre'
import { type Node, type Edge } from '@xyflow/react'
import type { EmployeeNodeData } from '../types'

const LAYOUT_CONFIG = {
    nodeWidth: 200,
    nodeHeight: 120,
    nodeSpacing: 25,
    rankSpacing: 25,
} as const

interface UseGraphLayoutOptions {
    nodeWidth?: number
    nodeHeight?: number
    nodeSpacing?: number
    rankSpacing?: number
}

/**
 * Custom hook for calculating graph layout using Dagre
 * Memoizes expensive layout calculations and provides clean API
 */
export function useGraphLayout(
    nodes: Node<EmployeeNodeData>[],
    edges: Edge[],
    options: UseGraphLayoutOptions = {},
) {
    const {
        nodeWidth = LAYOUT_CONFIG.nodeWidth,
        nodeHeight = LAYOUT_CONFIG.nodeHeight,
        nodeSpacing = LAYOUT_CONFIG.nodeSpacing,
        rankSpacing = LAYOUT_CONFIG.rankSpacing,
    } = options

    /**
     * Memoized layout calculation
     * Only recalculates when nodes, edges, or layout options change
     */
    const layoutedElements = useMemo(() => {
        if (nodes.length === 0) {
            return { nodes: [], edges }
        }

        // Create a new dagre graph instance for this calculation
        const dagreGraph = new dagre.graphlib.Graph()
        dagreGraph.setDefaultEdgeLabel(() => ({}))

        // Configure the graph
        dagreGraph.setGraph({
            rankdir: 'TB',
            nodesep: nodeSpacing,
            ranksep: rankSpacing,
        })

        // Add nodes to the graph
        for (const node of nodes) {
            dagreGraph.setNode(node.id, { width: nodeWidth, height: nodeHeight })
        }

        // Add edges to the graph
        for (const edge of edges) {
            dagreGraph.setEdge(edge.source, edge.target)
        }

        // Calculate layout
        dagre.layout(dagreGraph)

        // Apply calculated positions to nodes
        const layoutedNodes = nodes.map((node) => {
            const nodeWithPosition = dagreGraph.node(node.id)

            return {
                ...node,
                position: {
                    x: nodeWithPosition.x - nodeWidth / 2,
                    y: nodeWithPosition.y - nodeHeight / 2,
                },
            }
        })

        return { nodes: layoutedNodes, edges }
    }, [
        nodes,
        edges,
        nodeWidth,
        nodeHeight,
        nodeSpacing,
        rankSpacing,
    ])

    return {
        nodes: layoutedElements.nodes,
        edges: layoutedElements.edges,
        layoutConfig: {
            nodeWidth,
            nodeHeight,
            nodeSpacing,
            rankSpacing,
        },
    }
}

/**
 * Hook for getting layout statistics
 * Useful for debugging and optimization
 */
export function useLayoutStats(nodes: Node[], edges: Edge[]) {
    return useMemo(() => {
        const nodeCount = nodes.length
        const edgeCount = edges.length
        const departments = new Set(
            nodes
                .map((node) => (node.data as EmployeeNodeData)?.employee?.department)
                .filter(Boolean),
        ).size

        return {
            nodeCount,
            edgeCount,
            departments,
            complexity: nodeCount + edgeCount,
        }
    }, [nodes, edges])
}
