import { useQuery } from '@tanstack/react-query'
import { initialEdges, initialNodes } from '../data/mock-employees'
import type { OrganizationalData } from '../types'

// TODO PLACEHOLDER мок ответа сервера
const fetchOrganizationalData = async (): Promise<OrganizationalData> => {
    // delay
    await new Promise((resolve) => setTimeout(resolve, 500))

    return {
        nodes: initialNodes,
        edges: initialEdges,
    }
}

export function useOrganizationalData() {
    return useQuery({
        queryKey: ['company-graph'],
        queryFn: fetchOrganizationalData,
        staleTime: 5 * 60 * 1000,
    })
}
