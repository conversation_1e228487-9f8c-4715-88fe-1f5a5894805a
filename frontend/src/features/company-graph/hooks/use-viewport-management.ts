import { useCallback, useEffect, useRef, useState } from 'react'
import { useReactFlow } from '@xyflow/react'

interface UseViewportManagementOptions {
    /**
     * Node ID to focus on when initializing the viewport
     */
    focusNodeId?: string
    /**
     * Padding around the focused node
     */
    padding?: number
    /**
     * Percentage of viewport height to position the root node from top (0.0 to 1.0)
     * @default 0.25 (25% from top)
     */
    rootNodePositionRatio?: number
    /**
     * Whether to automatically focus on initialization
     */
    autoFocus?: boolean
    /**
     * Delay before applying focus (in milliseconds)
     */
    focusDelay?: number
    /**
     * Minimum vertical offset in pixels (fallback for very small screens)
     */
    minVerticalOffset?: number
    /**
     * Maximum vertical offset in pixels (prevent excessive offset on large screens)
     */
    maxVerticalOffset?: number
}

interface ViewportDimensions {
    width: number
    height: number
}

/**
 * Custom hook for responsive viewport management
 * Calculates dynamic offsets based on container dimensions for optimal positioning
 */
export function useViewportManagement(options: UseViewportManagementOptions = {}) {
    const {
        focusNodeId = 'ceo-001',
        padding = 5,
        rootNodePositionRatio = 0.25,
        autoFocus = true,
        focusDelay = 100,
        minVerticalOffset = 50,
        maxVerticalOffset = 400,
    } = options

    const { setViewport, fitView, getViewport, viewportInitialized } = useReactFlow()
    const hasInitialized = useRef(false)
    const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)
    const [containerDimensions, setContainerDimensions] = useState<ViewportDimensions>({
        width: 0,
        height: 0,
    })

    /**
     * Calculate responsive vertical offset based on container dimensions
     */
    const calculateVerticalOffset = useCallback(
        (containerHeight: number): number => {
            // Calculate offset to position root node at specified ratio from top
            const targetOffset = containerHeight * rootNodePositionRatio

            // Apply min/max constraints
            const constrainedOffset = Math.max(
                minVerticalOffset,
                Math.min(maxVerticalOffset, targetOffset),
            )

            // Return negative offset (moving up from center)
            return -constrainedOffset
        },
        [
            rootNodePositionRatio,
            minVerticalOffset,
            maxVerticalOffset,
        ],
    )

    /**
     * Get current container dimensions from React Flow
     */
    const getContainerDimensions = useCallback((): ViewportDimensions => {
        // Try to get dimensions from React Flow container
        const reactFlowWrapper = document.querySelector('.react-flow')
        if (reactFlowWrapper) {
            const rect = reactFlowWrapper.getBoundingClientRect()
            return {
                width: rect.width,
                height: rect.height,
            }
        }

        // Fallback to viewport dimensions
        return {
            width: window.innerWidth,
            height: window.innerHeight,
        }
    }, [])

    /**
     * Update container dimensions
     */
    const updateContainerDimensions = useCallback(() => {
        const dimensions = getContainerDimensions()
        setContainerDimensions(dimensions)
    }, [getContainerDimensions])

    /**
     * Focus on a specific node with responsive positioning
     */
    const focusOnNode = useCallback(
        async (nodeId: string, customPadding?: number, customPositionRatio?: number) => {
            try {
                if (!viewportInitialized) {
                    if (process.env.NODE_ENV === 'development') {
                        // eslint-disable-next-line no-console
                        console.warn('Viewport not initialized yet, skipping focus')
                    }
                    return
                }

                // Update container dimensions before calculating offset
                const currentDimensions = getContainerDimensions()

                // First, fit the view to center the node
                const success = await fitView({
                    nodes: [{ id: nodeId }],
                    padding: customPadding ?? padding,
                })

                if (success) {
                    const viewport = getViewport()

                    // Calculate responsive vertical offset
                    const positionRatio = customPositionRatio ?? rootNodePositionRatio
                    const verticalOffset = currentDimensions.height * positionRatio * -1

                    // Apply constraints
                    const constrainedOffset = Math.max(
                        -maxVerticalOffset,
                        Math.min(-minVerticalOffset, verticalOffset),
                    )

                    // Apply the calculated offset
                    setViewport({
                        x: viewport.x,
                        y: viewport.y + constrainedOffset,
                        zoom: viewport.zoom,
                    })
                }
            } catch (error) {
                if (process.env.NODE_ENV === 'development') {
                    // eslint-disable-next-line no-console
                    console.error('Failed to focus on node:', error)
                }
            }
        },
        [
            viewportInitialized,
            fitView,
            getViewport,
            setViewport,
            padding,
            rootNodePositionRatio,
            minVerticalOffset,
            maxVerticalOffset,
            getContainerDimensions,
        ],
    )

    /**
     * Center the view on all nodes with responsive positioning
     */
    const centerView = useCallback(async () => {
        try {
            if (!viewportInitialized) {
                if (process.env.NODE_ENV === 'development') {
                    // eslint-disable-next-line no-console
                    console.warn('Viewport not initialized yet, skipping center')
                }
                return
            }

            await fitView({ padding })
        } catch (error) {
            if (process.env.NODE_ENV === 'development') {
                // eslint-disable-next-line no-console
                console.error('Failed to center view:', error)
            }
        }
    }, [
        viewportInitialized,
        fitView,
        padding,
    ])

    /**
     * Reset viewport to default position
     */
    const resetViewport = useCallback(() => {
        setViewport({ x: 0, y: 0, zoom: 1 })
    }, [setViewport])

    /**
     * Get current viewport information
     */
    const getCurrentViewport = useCallback(() => {
        return getViewport()
    }, [getViewport])

    /**
     * Auto-focus effect with proper cleanup and responsive handling
     */
    useEffect(() => {
        if (!autoFocus || !viewportInitialized || hasInitialized.current) {
            return
        }

        // Update dimensions when viewport is initialized
        updateContainerDimensions()

        // Clear any existing timeout
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
        }

        // Delay the focus to ensure the graph is fully rendered
        timeoutRef.current = setTimeout(() => {
            focusOnNode(focusNodeId)
            hasInitialized.current = true
        }, focusDelay)

        // Cleanup function
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current)
            }
        }
    }, [
        viewportInitialized,
        autoFocus,
        focusNodeId,
        focusDelay,
        focusOnNode,
        updateContainerDimensions,
    ])

    /**
     * Handle window resize to update container dimensions
     */
    useEffect(() => {
        const handleResize = () => {
            updateContainerDimensions()
        }

        window.addEventListener('resize', handleResize)

        // Initial dimension update
        updateContainerDimensions()

        return () => {
            window.removeEventListener('resize', handleResize)
        }
    }, [updateContainerDimensions])

    /**
     * Cleanup on unmount
     */
    useEffect(() => {
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current)
            }
        }
    }, [])

    return {
        // Actions
        focusOnNode,
        centerView,
        resetViewport,

        // State
        viewportInitialized,
        getCurrentViewport,
        containerDimensions,

        // Configuration
        isAutoFocusEnabled: autoFocus,
        focusNodeId,

        // Utilities
        calculateVerticalOffset,
        updateContainerDimensions,
    }
}
