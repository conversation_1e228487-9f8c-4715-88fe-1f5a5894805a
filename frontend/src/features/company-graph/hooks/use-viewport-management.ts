import { useCallback } from 'react'
import { useReactFlow } from '@xyflow/react'

export function useViewportManagement(nodeId: string, padding: number) {
    const { setViewport, fitView, getViewport } = useReactFlow()

    const focusOnNode = useCallback(async () => {
        await fitView({ nodes: [{ id: nodeId }], padding })
        const viewport = getViewport()

        setViewport({
            x: viewport.x,
            y: viewport.y - 300,
            zoom: viewport.zoom,
        })
    }, [
        fitView,
        nodeId,
        padding,
        getViewport,
        setViewport,
    ])

    return { focusOnNode }
}
