/**
 * Company Graph Feature
 *
 * A modern, performant organizational chart component built with:
 * - React Flow for graph visualization
 * - TanStack Query for data management
 * - Dagre for automatic layout
 * - TypeScript for type safety
 * - Comprehensive error handling and loading states
 *
 * @example
 * ```tsx
 * import { CompanyGraph } from '@/features/company-graph'
 *
 * function OrganizationPage() {
 *   return (
 *     <div className="w-full h-screen">
 *       <CompanyGraph
 *         layoutDirection="TB"
 *         autoFocus={true}
 *         focusNodeId="ceo-001"
 *       />
 *     </div>
 *   )
 * }
 * ```
 */

// Main component export
export { CompanyGraph } from './company-graph'

// Hook exports for advanced usage
export {
    useOrganizationalData,
    useGraphLayout,
    useLayoutStats,
    useViewportManagement,
} from './hooks'

// Component exports for custom implementations
export {
    CompanyGraphFlow,
    CompanyGraphLoading,
    CompanyGraphError,
    CompanyGraphErrorBoundary,
    withErrorBoundary,
} from './components'

// Type exports
export type {
    Employee,
    EmployeeNode,
    EmployeeEdge,
    EmployeeNodeData,
    EmployeeEdgeData,
    OrganizationalData,
    DepartmentStats,
    HierarchyStats,
    GraphLayoutConfig,
    GraphError,
    LoadingState,
    SearchOptions,
    BaseComponentProps,
} from './types'
