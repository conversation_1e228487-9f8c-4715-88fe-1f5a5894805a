import type { Node, Edge } from '@xyflow/react'

/**
 * Core employee data structure
 */
export interface Employee {
    /** Unique identifier for the employee */
    id: string
    /** Full name of the employee */
    name: string
    /** Job title/position */
    position: string
    /** Department the employee belongs to */
    department: string
    /** Email address */
    email: string
    /** Optional phone number */
    phone?: string
    /** Optional avatar URL */
    avatar?: string
    /** Hierarchy level (0 = CEO, 1 = C-level, etc.) */
    level: number
    /** Optional manager ID for hierarchy */
    managerId?: string
    /** Optional start date */
    startDate?: string
    /** Optional employee status */
    status?: 'active' | 'inactive' | 'on-leave'
}

/**
 * Data structure for employee nodes in React Flow
 */
export interface EmployeeNodeData extends Record<string, unknown> {
    employee: Employee
    /** Optional highlight state */
    isHighlighted?: boolean
    /** Optional selection state */
    isSelected?: boolean
}

/**
 * Type-safe employee node for React Flow
 */
export type EmployeeNode = Node<EmployeeNodeData>

/**
 * Data structure for edges representing relationships
 */
export interface EmployeeEdgeData extends Record<string, unknown> {
    relationship: 'reports-to' | 'manages' | 'collaborates'
    /** Optional edge label */
    label?: string
    /** Optional edge weight for layout */
    weight?: number
}

/**
 * Type-safe employee edge for React Flow
 */
export type EmployeeEdge = Edge

/**
 * Complete organizational data structure
 */
export interface OrganizationalData {
    nodes: EmployeeNode[]
    edges: EmployeeEdge[]
}

/**
 * Department statistics
 */
export interface DepartmentStats {
    name: string
    employeeCount: number
    levels: number[]
    headId?: string
}

/**
 * Hierarchy statistics
 */
export interface HierarchyStats {
    totalLevels: number
    employeesPerLevel: Record<number, number>
    departments: DepartmentStats[]
    totalEmployees: number
}

/**
 * Graph layout configuration
 */
export interface GraphLayoutConfig {
    direction: 'TB' | 'LR' | 'BT' | 'RL'
    nodeWidth: number
    nodeHeight: number
    nodeSpacing: number
    rankSpacing: number
}

/**
 * Error types for better error handling
 */
export type GraphError =
    | { type: 'FETCH_ERROR'; message: string; cause?: Error }
    | { type: 'LAYOUT_ERROR'; message: string; nodeId?: string }
    | { type: 'RENDER_ERROR'; message: string; componentName?: string }
    | { type: 'VALIDATION_ERROR'; message: string; field?: string }

/**
 * Loading states for different operations
 */
export type LoadingState = 'idle' | 'loading' | 'success' | 'error' | 'refetching'

/**
 * Search and filter options
 */
export interface SearchOptions {
    query: string
    department?: string
    level?: number
    status?: Employee['status']
}

/**
 * Component props for better type safety
 */
export interface BaseComponentProps {
    className?: string
    'data-testid'?: string
}
