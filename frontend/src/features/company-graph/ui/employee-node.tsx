import { <PERSON><PERSON>, Position } from '@xyflow/react'
import { memo, useMemo, useCallback } from 'react'
import type { EmployeeNodeData, BaseComponentProps } from '../types'

interface EmployeeNodeProps extends BaseComponentProps {
    data: EmployeeNodeData
    /**
     * Whether the node is currently selected
     */
    selected?: boolean
    /**
     * Whether the node is currently being dragged
     */
    dragging?: boolean
    /**
     * Callback when node is clicked
     */
    onClick?: (employeeId: string) => void
    /**
     * Callback when node is double-clicked
     */
    onDoubleClick?: (employeeId: string) => void
}

/**
 * Styling configuration based on employee level
 * Cached to prevent recalculation on every render
 */
const LEVEL_STYLES = {
    0: {
        // CEO
        bg: 'bg-gradient-to-br from-purple-50 to-purple-100',
        border: 'border-purple-300',
        accent: 'text-purple-700',
        ring: 'ring-purple-200',
    },
    1: {
        // C-Level
        bg: 'bg-gradient-to-br from-blue-50 to-blue-100',
        border: 'border-blue-300',
        accent: 'text-blue-700',
        ring: 'ring-blue-200',
    },
    2: {
        // Department Heads
        bg: 'bg-gradient-to-br from-green-50 to-green-100',
        border: 'border-green-300',
        accent: 'text-green-700',
        ring: 'ring-green-200',
    },
    3: {
        // Senior Staff
        bg: 'bg-gradient-to-br from-orange-50 to-orange-100',
        border: 'border-orange-300',
        accent: 'text-orange-700',
        ring: 'ring-orange-200',
    },
    default: {
        // Others
        bg: 'bg-gradient-to-br from-gray-50 to-gray-100',
        border: 'border-gray-300',
        accent: 'text-gray-700',
        ring: 'ring-gray-200',
    },
} as const

/**
 * Optimized Employee Node component with performance enhancements
 * Uses React.memo and useMemo to prevent unnecessary re-renders
 */
export const EmployeeNode = memo(function EmployeeNode({
    data,
    selected = false,
    dragging = false,
    onClick,
    onDoubleClick,
    className = '',
    'data-testid': testId,
}: EmployeeNodeProps) {
    const { employee, isHighlighted = false } = data

    // Memoize avatar URL to prevent recalculation
    const avatarUrl = useMemo(() => {
        if (employee.avatar) {
            return employee.avatar
        }

        const encodedName = encodeURIComponent(employee.name)
        return `https://ui-avatars.com/api/?name=${encodedName}&background=6366f1&color=fff&size=56&rounded=true`
    }, [employee.avatar, employee.name])

    // Memoize styling based on employee level
    const styling = useMemo(() => {
        const levelKey = employee.level as keyof typeof LEVEL_STYLES
        return LEVEL_STYLES[levelKey] || LEVEL_STYLES.default
    }, [employee.level])

    // Memoize CSS classes to prevent recalculation
    const nodeClasses = useMemo(() => {
        const baseClasses = [
            'relative',
            'min-w-[200px]',
            'max-w-[200px]',
            'rounded-2xl',
            'border-2',
            'shadow-md',
            'p-3',
            'transition-all',
            'duration-200',
            'will-change-transform',
            'cursor-pointer',
            styling.bg,
            styling.border,
        ]

        // Add conditional classes
        if (selected || isHighlighted) {
            baseClasses.push('ring-4', styling.ring, 'shadow-lg', 'scale-105')
        }

        if (dragging) {
            baseClasses.push('shadow-2xl', 'rotate-1')
        }

        if (!dragging && !selected) {
            baseClasses.push('hover:scale-[1.02]', 'hover:shadow-lg')
        }

        if (className) {
            baseClasses.push(className)
        }

        return baseClasses.join(' ')
    }, [
        styling,
        selected,
        isHighlighted,
        dragging,
        className,
    ])

    // Memoize handle classes
    const handleClasses = useMemo(
        () =>
            'w-2 h-2 bg-white/80 border border-gray-300/50 shadow-sm transition-colors duration-200 hover:bg-white',
        [],
    )

    // Optimized click handlers
    const handleClick = useCallback(() => {
        onClick?.(employee.id)
    }, [onClick, employee.id])

    const handleDoubleClick = useCallback(() => {
        onDoubleClick?.(employee.id)
    }, [onDoubleClick, employee.id])

    return (
        <div
            className={nodeClasses}
            onClick={handleClick}
            onDoubleClick={handleDoubleClick}
            data-testid={testId || `employee-node-${employee.id}`}
            role="button"
            tabIndex={0}
            aria-label={`Employee: ${employee.name}, Position: ${employee.position}`}
        >
            {/* Top handle for incoming connections */}
            <Handle
                type="target"
                position={Position.Top}
                className={handleClasses}
                aria-label="Connection point"
            />

            {/* Avatar */}
            <div className="flex-shrink-0 absolute -top-7 left-1/2 transform -translate-x-1/2">
                <img
                    src={avatarUrl}
                    alt={`${employee.name} avatar`}
                    className="w-14 h-14 rounded-full object-cover border-2 border-white shadow-md"
                    loading="lazy"
                    onError={(e) => {
                        // Fallback to initials if image fails to load
                        const target = e.target as HTMLImageElement
                        target.style.display = 'none'
                        target.nextElementSibling?.classList.remove('hidden')
                        target.nextElementSibling?.classList.add('flex')
                    }}
                />
                {/* Fallback initials */}
                <div className="hidden w-14 h-14 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 items-center justify-center border-2 border-white shadow-md">
                    <span className="text-white font-bold text-lg">
                        {employee.name
                            .split(' ')
                            .map((n) => n[0])
                            .join('')
                            .toUpperCase()}
                    </span>
                </div>
            </div>

            {/* Content */}
            <div className="flex flex-col items-center justify-center pt-8 pb-2 px-2">
                <h3
                    className={`font-semibold text-sm leading-tight mb-1 text-center ${styling.accent}`}
                >
                    {employee.name}
                </h3>
                <p className="text-xs text-gray-600 font-medium text-center leading-tight">
                    {employee.position}
                </p>
                {employee.department && (
                    <p className="text-xs text-gray-500 text-center mt-1">{employee.department}</p>
                )}
            </div>

            {/* Bottom handle for outgoing connections */}
            <Handle
                type="source"
                position={Position.Bottom}
                className={handleClasses}
                aria-label="Connection point"
            />

            {/* Status indicator */}
            {employee.status && employee.status !== 'active' && (
                <div className="absolute top-2 right-2">
                    <div
                        className={`w-2 h-2 rounded-full ${
                            employee.status === 'on-leave' ? 'bg-yellow-400' : 'bg-red-400'
                        }`}
                        title={employee.status === 'on-leave' ? 'В отпуске' : 'Неактивен'}
                    />
                </div>
            )}
        </div>
    )
})

EmployeeNode.displayName = 'EmployeeNode'
