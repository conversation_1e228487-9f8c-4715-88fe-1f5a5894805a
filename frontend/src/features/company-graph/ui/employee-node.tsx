import { <PERSON><PERSON>, Position } from '@xyflow/react'
import { Mail, Phone } from 'lucide-react'
import { memo, useMemo } from 'react'
import type { EmployeeNodeData } from '../types'

interface EmployeeNodeProps {
    data: EmployeeNodeData
}

// Define styling type for better performance and type safety
interface CardStyling {
    bg: string
    border: string
    accent: string
    textPrimary: string
    textSecondary: string
}

// Memoized styling cache to prevent recalculation on every render
const CARD_STYLING_CACHE = new Map<number, CardStyling>()

// Determine card styling based on employee level - Optimized for performance

// Memoized component to prevent unnecessary re-renders
const EmployeeNodeComponent = memo(function EmployeeNode({ data }: EmployeeNodeProps) {
    const { employee } = data

    // Memoize avatar URL to prevent recalculation
    const avatarUrl = useMemo(
        () =>
            employee.avatar ||
            `https://ui-avatars.com/api/?name=${encodeURIComponent(employee.name)}&background=6366f1&color=fff&size=56`,
        [employee.avatar, employee.name],
    )

    return (
        <div
            className={`bg-white  rounded-2xl border shadow-md p-3 min-w-[200px] max-w-[200px] transition-transform duration-150 hover:scale-[1.01] relative will-change-transform`}
        >
            <Handle
                type="target"
                position={Position.Top}
                className="w-2 h-2 bg-white/80 border border-gray-300/50 shadow-sm"
            />

            <div className="flex items-center justify-center gap-3">
                <div className="flex-shrink-0 absolute -top-7 left-18">
                    <img
                        src={avatarUrl}
                        alt={employee.name}
                        className="w-14 h-14 rounded-full object-cover border-2 border-white/50 shadow-sm"
                        loading="lazy"
                    />
                </div>

                <div className="flex-1 flex flex-col justify-center items-center min-w-0 pt-5">
                    <h3 className={`font-semibold text-primary text-sm leading-tight mb-1`}>
                        {employee.name}
                    </h3>
                    <p className={`text-xs text-primary font-medium mb-1 leading-tight`}>
                        {employee.position}
                    </p>
                </div>
            </div>

            <Handle
                type="source"
                position={Position.Bottom}
                className="w-2 h-2 bg-white/80 border border-gray-300/50 shadow-sm"
            />
        </div>
    )
})

// Export the memoized component
export { EmployeeNodeComponent as EmployeeNode }
